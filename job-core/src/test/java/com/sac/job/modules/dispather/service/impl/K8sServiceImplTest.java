package com.sac.job.modules.dispather.service.impl;

import com.sac.job.k8s.bo.*;
import com.sac.job.modules.dispather.service.K8sService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * K8s 服务测试类
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class K8sServiceImplTest {
    
    @Autowired(required = false)
    private K8sService k8sService;
    
    @Test
    void testConnection() {
        if (k8sService == null) {
            log.info("K8s 服务未启用，跳过测试");
            return;
        }
        
        try {
            boolean connected = k8sService.isConnected();
            log.info("K8s 连接状态: {}", connected);
        } catch (Exception e) {
            log.error("测试连接失败", e);
        }
    }
    
    @Test
    void testGetClusterResources() {
        if (k8sService == null) {
            log.info("K8s 服务未启用，跳过测试");
            return;
        }
        
        try {
            ClusterResourceInfo resourceInfo = k8sService.getClusterResources();
            log.info("集群资源信息: {}", resourceInfo);
        } catch (Exception e) {
            log.error("获取集群资源信息失败", e);
        }
    }
    
    @Test
    void testListPods() {
        if (k8sService == null) {
            log.info("K8s 服务未启用，跳过测试");
            return;
        }
        
        try {
            List<PodInfo> pods = k8sService.listAllPods();
            log.info("Pod 数量: {}", pods.size());
            pods.forEach(pod -> log.info("Pod: {} - {}", pod.getName(), pod.getPhase()));
        } catch (Exception e) {
            log.error("列出 Pod 失败", e);
        }
    }
    
    @Test
    void testCreateAndDeletePod() {
        if (k8sService == null) {
            log.info("K8s 服务未启用，跳过测试");
            return;
        }
        
        try {
            // 创建测试 Pod 规格
            PodSpec podSpec = createTestPodSpec();
            
            // 创建 Pod
            String podName = k8sService.createPod(podSpec, "default");
            log.info("创建 Pod 成功: {}", podName);
            
            // 等待一段时间
            Thread.sleep(5000);
            
            // 获取 Pod 信息
            PodInfo podInfo = k8sService.getPodInfo(podName, "default");
            log.info("Pod 信息: {}", podInfo);
            
            // 删除 Pod
            boolean deleted = k8sService.deletePod(podName, "default");
            log.info("删除 Pod 结果: {}", deleted);
            
        } catch (Exception e) {
            log.error("创建和删除 Pod 测试失败", e);
        }
    }
    
    /**
     * 创建测试 Pod 规格
     */
    private PodSpec createTestPodSpec() {
        // 创建容器规格
        ContainerSpec containerSpec = ContainerSpec.builder()
                .name("test-container")
                .image("nginx:latest")
                .imagePullPolicy("IfNotPresent")
                .resources(ResourceRequirements.builder()
                        .cpuRequest(100L)
                        .memoryRequest(128 * 1024 * 1024L)
                        .cpuLimit(200L)
                        .memoryLimit(256 * 1024 * 1024L)
                        .build())
                .build();
        
        // 创建标签
        Map<String, String> labels = new HashMap<>();
        labels.put("app", "test-app");
        labels.put("version", "v1.0");
        
        // 创建 Pod 规格
        return PodSpec.builder()
                .name("test-pod-" + System.currentTimeMillis())
                .namespace("default")
                .labels(labels)
                .containers(Arrays.asList(containerSpec))
                .restartPolicy("Never")
                .build();
    }
}
