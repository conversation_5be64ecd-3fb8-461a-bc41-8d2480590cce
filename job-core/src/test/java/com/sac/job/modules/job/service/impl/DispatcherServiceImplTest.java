package com.sac.job.modules.job.service.impl;

import com.sac.job.modules.dispather.service.DispatcherService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

class DispatcherServiceImplTest {

    @Spy
    @InjectMocks
    DispatcherService dispatcherService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void test_exec_project() {
        String projectId = "123";
        dispatcherService.execProject(projectId);
    }

    @Test
    void test_debug_project() {
        String projectId = "123";
//        dispatcherService.debugProject(projectId);
    }

    @Test
    void test_exec_plan() {
        String planId = "123";
//        dispatcherService.execPlan(planId);
    }
}