package com.sac.job.configure;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


/**
 * fegin 調用配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2022/06/20 09:23
 */
@Configuration
public class OpenFeignConfig implements RequestInterceptor {


    @Value("${spring.application.name}")
    private String applicationName;


    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null && !requestTemplate.headers().containsKey("application-name")) {
            requestTemplate.header("application-Name", applicationName);
        }
    }

}

