package com.sac.job.configure;

import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.BatchV1Api;
import io.kubernetes.client.util.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * K8s 配置类
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "k8s", name = "enabled", havingValue = "true", matchIfMissing = false)
public class K8sConfig {
    
    @Value("${k8s.config.kubeconfig:}")
    private String kubeconfigPath;
    
    @Value("${k8s.config.in-cluster:false}")
    private boolean inCluster;
    
    @Value("${k8s.config.master-url:}")
    private String masterUrl;
    
    @Value("${k8s.config.namespace:default}")
    private String defaultNamespace;
    
    @Value("${k8s.config.connection-timeout:30000}")
    private int connectionTimeout;
    
    @Value("${k8s.config.read-timeout:60000}")
    private int readTimeout;
    
    /**
     * 创建 K8s API 客户端
     */
    @Bean
    public ApiClient apiClient() throws IOException {
        ApiClient client;
        
        if (inCluster) {
            // 在集群内运行，使用 ServiceAccount
            log.info("使用集群内配置初始化 K8s 客户端");
            client = Config.fromCluster();
        } else if (kubeconfigPath != null && !kubeconfigPath.isEmpty()) {
            // 使用指定的 kubeconfig 文件
            log.info("使用 kubeconfig 文件初始化 K8s 客户端: {}", kubeconfigPath);
            client = Config.fromConfig(kubeconfigPath);
        } else {
            // 使用默认配置
            log.info("使用默认配置初始化 K8s 客户端");
            client = Config.defaultClient();
        }
        
        // 设置超时时间
        client.setConnectTimeout(connectionTimeout);
        client.setReadTimeout(readTimeout);
        
        // 设置为全局默认客户端
        io.kubernetes.client.openapi.Configuration.setDefaultApiClient(client);
        
        log.info("K8s 客户端初始化完成，服务器地址: {}", client.getBasePath());
        return client;
    }
    
    /**
     * 核心 API
     */
    @Bean
    public CoreV1Api coreV1Api(ApiClient apiClient) {
        return new CoreV1Api(apiClient);
    }
    
    /**
     * 应用 API
     */
    @Bean
    public AppsV1Api appsV1Api(ApiClient apiClient) {
        return new AppsV1Api(apiClient);
    }
    
    /**
     * 批处理 API
     */
    @Bean
    public BatchV1Api batchV1Api(ApiClient apiClient) {
        return new BatchV1Api(apiClient);
    }
    
    /**
     * 获取默认命名空间
     */
    @Bean("defaultNamespace")
    public String defaultNamespace() {
        return defaultNamespace;
    }
}
