package com.sac.job.configure.datasource.config;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * All rights Reserved, Designed By www.sac.com
 *
 * <AUTHOR>
 * @version V1.0.0
 * @projectName 火电代码
 * @title DynamicDataSource
 * @package com.sac.thermal.web.config.datasource.config
 * @description ${TODO}
 * @date 2019/10/23 11:14
 * @copyright 2019 www.sac.com
 * 注意 本内容仅限于国电南自,禁止外泄以及用于其他的商业
 */
public class DynamicDataSource extends AbstractRoutingDataSource {

    @Override
    protected Object determineCurrentLookupKey() {
        // TODO Auto-generated method stub
        return DynamicDataSourceHolder.getDataSource();
    }

}

