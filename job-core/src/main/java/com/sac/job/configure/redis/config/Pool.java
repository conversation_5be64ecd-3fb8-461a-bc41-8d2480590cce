package com.sac.job.configure.redis.config;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @author: wwg
 * @date: 2022/06/20 09:33
 * @description: TODO
 * @copyright: 2019 南京华盾电力信息安全测评有限公司 All rights reserved.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Pool {


    private Integer maxWait;

    private Integer maxIdle;

    private Integer minIdle;
}
