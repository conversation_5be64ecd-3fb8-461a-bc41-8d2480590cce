package com.sac.job.modules.dispather.service;

import com.sac.job.job.bo.JobK8sConfigBO;
import com.sac.job.k8s.bo.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * K8s 服务接口
 */
public interface K8sService {

    // ========== 原有方法 ==========

    /**
     * 检查资源是否充足
     */
    boolean notEnoughResources();

    /**
     * 运行作业
     */
    void run(JobK8sConfigBO config);

    // ========== Pod 管理 ==========

    /**
     * 创建 Pod
     */
    String createPod(PodSpec podSpec, String namespace);

    /**
     * 删除 Pod
     */
    boolean deletePod(String podName, String namespace);

    /**
     * 获取 Pod 信息
     */
    PodInfo getPodInfo(String podName, String namespace);

    /**
     * 列出 Pod
     */
    List<PodInfo> listPods(String namespace);

    /**
     * 列出所有命名空间的 Pod
     */
    List<PodInfo> listAllPods();

    // ========== 容器状态查询 ==========

    /**
     * 获取容器状态
     */
    ContainerInfo getContainerStatus(String podName, String containerName, String namespace);

    /**
     * 获取 Pod 中所有容器状态
     */
    List<ContainerInfo> getPodContainers(String podName, String namespace);

    // ========== 资源监控 ==========

    /**
     * 获取 Pod 资源使用情况
     */
    ResourceUsage getResourceUsage(String podName, String namespace);

    /**
     * 获取集群资源信息
     */
    ClusterResourceInfo getClusterResources();

    /**
     * 获取节点信息
     */
    List<NodeInfo> getNodes();

    // ========== 扩缩容 ==========

    /**
     * 扩缩容 Deployment
     */
    boolean scaleDeployment(String deploymentName, int replicas, String namespace);

    // ========== 日志管理 ==========

    /**
     * 获取 Pod 日志
     */
    String getPodLogs(String podName, String namespace);

    /**
     * 获取容器日志
     */
    String getContainerLogs(String podName, String containerName, String namespace);

    /**
     * 获取 Pod 日志（带参数）
     */
    String getPodLogs(String podName, String namespace, Integer tailLines, Boolean follow);

    // ========== 事件监控 ==========

    /**
     * 获取 Pod 事件
     */
    List<K8sEvent> getPodEvents(String podName, String namespace);

    /**
     * 监听 Pod 状态变化
     */
    void watchPodStatus(String namespace, Consumer<PodInfo> callback);

    /**
     * 异步监听 Pod 状态变化
     */
    CompletableFuture<Void> watchPodStatusAsync(String namespace, Consumer<PodInfo> callback);

    // ========== 健康检查 ==========

    /**
     * 检查 K8s 连接状态
     */
    boolean isConnected();

    /**
     * 获取 K8s 版本信息
     */
    String getVersion();
}
