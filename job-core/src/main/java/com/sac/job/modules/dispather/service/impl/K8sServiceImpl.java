package com.sac.job.modules.dispather.service.impl;

import com.sac.job.job.bo.JobK8sConfigBO;
import com.sac.job.modules.dispather.service.K8sService;
import org.springframework.stereotype.Service;

@Service
public class K8sServiceImpl implements K8sService {
    @Override
    public boolean notEnoughResources() {
        throw new UnsupportedOperationException();
    }

    @Override
    public void run(JobK8sConfigBO config) {
        throw new UnsupportedOperationException();
    }
}
