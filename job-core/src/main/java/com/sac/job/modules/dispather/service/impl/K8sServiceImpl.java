package com.sac.job.modules.dispather.service.impl;

import com.sac.job.job.bo.JobK8sConfigBO;
import com.sac.job.k8s.bo.*;
import com.sac.job.modules.dispather.service.K8sService;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.BatchV1Api;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.apis.VersionApi;
import io.kubernetes.client.openapi.models.*;
import io.kubernetes.client.openapi.models.VersionInfo;
import io.kubernetes.client.util.Watch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;

import javax.annotation.PreDestroy;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * K8s 服务实现类
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "k8s", name = "enabled", havingValue = "true", matchIfMissing = false)
public class K8sServiceImpl implements K8sService {

    @Autowired
    private CoreV1Api coreV1Api;

    @Autowired
    private AppsV1Api appsV1Api;

    @Autowired
    private BatchV1Api batchV1Api;

    @Autowired
    private ApiClient apiClient;

    @Autowired
    @Qualifier("defaultNamespace")
    private String defaultNamespace;

    // 用于调度清理任务的线程池
    private final ScheduledExecutorService cleanupExecutor = Executors.newScheduledThreadPool(2);

    // ========== 原有方法实现 ==========

    @Override
    public boolean notEnoughResources() {
        try {
            ClusterResourceInfo clusterInfo = getClusterResources();

            // 检查 CPU 使用率是否超过 80%
            double cpuUsagePercent = (double) clusterInfo.getUsedCpu() / clusterInfo.getAllocatableCpu() * 100;
            if (cpuUsagePercent > 80) {
                log.warn("集群 CPU 使用率过高: {}%", cpuUsagePercent);
                return true;
            }

            // 检查内存使用率是否超过 80%
            double memoryUsagePercent = (double) clusterInfo.getUsedMemory() / clusterInfo.getAllocatableMemory() * 100;
            if (memoryUsagePercent > 80) {
                log.warn("集群内存使用率过高: {}%", memoryUsagePercent);
                return true;
            }

            // 检查就绪节点数
            if (clusterInfo.getReadyNodes() < clusterInfo.getTotalNodes()) {
                log.warn("存在未就绪节点: {}/{}", clusterInfo.getReadyNodes(), clusterInfo.getTotalNodes());
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("检查集群资源时发生错误", e);
            return true; // 发生错误时认为资源不足
        }
    }

    @Override
    public void run(JobK8sConfigBO config) {
        try {
            log.info("开始运行 K8s 作业: {}", config.getJobName());

            // 创建 Pod
            String podName = createPod(config.getPodSpec(), config.getNamespace());
            log.info("成功创建 Pod: {}", podName);

            // 如果配置了自动清理，则设置清理任务
            if (Boolean.TRUE.equals(config.getAutoCleanup())) {
                scheduleCleanup(podName, config.getNamespace(), config.getCleanupDelaySeconds());
            }

        } catch (Exception e) {
            log.error("运行 K8s 作业失败: {}", config.getJobName(), e);
            throw new RuntimeException("运行 K8s 作业失败", e);
        }
    }

    // ========== Pod 管理实现 ==========

    @Override
    public String createPod(PodSpec podSpec, String namespace) {
        try {
            V1Pod pod = buildV1Pod(podSpec);
            V1Pod createdPod = coreV1Api.createNamespacedPod(
                namespace != null ? namespace : defaultNamespace,
                pod,
                null, null, null, null
            );

            String podName = createdPod.getMetadata().getName();
            log.info("成功创建 Pod: {} 在命名空间: {}", podName, namespace);
            return podName;

        } catch (ApiException e) {
            log.error("创建 Pod 失败: {}", e.getResponseBody(), e);
            throw new RuntimeException("创建 Pod 失败", e);
        }
    }

    @Override
    public boolean deletePod(String podName, String namespace) {
        try {
            coreV1Api.deleteNamespacedPod(
                podName,
                namespace != null ? namespace : defaultNamespace,
                null, null, null, null, null, null
            );

            log.info("成功删除 Pod: {} 在命名空间: {}", podName, namespace);
            return true;

        } catch (ApiException e) {
            if (e.getCode() == 404) {
                log.warn("Pod 不存在: {} 在命名空间: {}", podName, namespace);
                return true; // Pod 不存在也认为删除成功
            }
            log.error("删除 Pod 失败: {}", e.getResponseBody(), e);
            return false;
        }
    }

    @Override
    public PodInfo getPodInfo(String podName, String namespace) {
        try {
            V1Pod pod = coreV1Api.readNamespacedPod(
                podName,
                namespace != null ? namespace : defaultNamespace,
                null
            );

            return convertToPodInfo(pod);

        } catch (ApiException e) {
            if (e.getCode() == 404) {
                log.warn("Pod 不存在: {} 在命名空间: {}", podName, namespace);
                return null;
            }
            log.error("获取 Pod 信息失败: {}", e.getResponseBody(), e);
            throw new RuntimeException("获取 Pod 信息失败", e);
        }
    }

    @Override
    public List<PodInfo> listPods(String namespace) {
        try {
            V1PodList podList = coreV1Api.listNamespacedPod(
                namespace != null ? namespace : defaultNamespace,
                null, null, null, null, null, null, null, null, null, null
            );

            return podList.getItems().stream()
                    .map(this::convertToPodInfo)
                    .collect(Collectors.toList());

        } catch (ApiException e) {
            log.error("列出 Pod 失败: {}", e.getResponseBody(), e);
            throw new RuntimeException("列出 Pod 失败", e);
        }
    }

    @Override
    public List<PodInfo> listAllPods() {
        try {
            V1PodList podList = coreV1Api.listPodForAllNamespaces(
                null, null, null, null, null, null, null, null, null, null
            );

            return podList.getItems().stream()
                    .map(this::convertToPodInfo)
                    .collect(Collectors.toList());

        } catch (ApiException e) {
            log.error("列出所有 Pod 失败: {}", e.getResponseBody(), e);
            throw new RuntimeException("列出所有 Pod 失败", e);
        }
    }

    // ========== 容器状态查询实现 ==========

    @Override
    public ContainerInfo getContainerStatus(String podName, String containerName, String namespace) {
        PodInfo podInfo = getPodInfo(podName, namespace);
        if (podInfo == null) {
            return null;
        }

        return podInfo.getContainers().stream()
                .filter(container -> containerName.equals(container.getName()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public List<ContainerInfo> getPodContainers(String podName, String namespace) {
        PodInfo podInfo = getPodInfo(podName, namespace);
        return podInfo != null ? podInfo.getContainers() : Collections.emptyList();
    }

    // ========== 资源监控实现 ==========

    @Override
    public ResourceUsage getResourceUsage(String podName, String namespace) {
        try {
            // 注意：这里需要 metrics-server 支持
            // 实际实现中可能需要调用 metrics API
            log.warn("资源使用情况监控需要 metrics-server 支持，当前返回模拟数据");

            return ResourceUsage.builder()
                    .podName(podName)
                    .namespace(namespace)
                    .cpuUsage(100L) // 模拟数据
                    .memoryUsage(128 * 1024 * 1024L) // 模拟数据
                    .build();

        } catch (Exception e) {
            log.error("获取资源使用情况失败", e);
            return null;
        }
    }

    @Override
    public ClusterResourceInfo getClusterResources() {
        try {
            V1NodeList nodeList = coreV1Api.listNode(null, null, null, null, null, null, null, null, null, null);
            V1PodList podList = coreV1Api.listPodForAllNamespaces(null, null, null, null, null, null, null, null, null, null);

            List<NodeInfo> nodes = nodeList.getItems().stream()
                    .map(this::convertToNodeInfo)
                    .collect(Collectors.toList());

            // 计算集群总资源
            long totalCpu = nodes.stream().mapToLong(NodeInfo::getCpuCapacity).sum();
            long totalMemory = nodes.stream().mapToLong(NodeInfo::getMemoryCapacity).sum();
            long totalStorage = nodes.stream().mapToLong(NodeInfo::getStorageCapacity).sum();

            // 计算已使用资源（简化计算）
            long usedCpu = totalCpu / 2; // 模拟数据
            long usedMemory = totalMemory / 2; // 模拟数据
            long usedStorage = totalStorage / 2; // 模拟数据

            int readyNodes = (int) nodes.stream().filter(NodeInfo::getReady).count();
            int runningPods = (int) podList.getItems().stream()
                    .filter(pod -> "Running".equals(pod.getStatus().getPhase()))
                    .count();

            return ClusterResourceInfo.builder()
                    .totalNodes(nodes.size())
                    .readyNodes(readyNodes)
                    .totalCpu(totalCpu)
                    .allocatableCpu(totalCpu)
                    .usedCpu(usedCpu)
                    .totalMemory(totalMemory)
                    .allocatableMemory(totalMemory)
                    .usedMemory(usedMemory)
                    .totalStorage(totalStorage)
                    .allocatableStorage(totalStorage)
                    .usedStorage(usedStorage)
                    .totalPods(podList.getItems().size())
                    .runningPods(runningPods)
                    .nodes(nodes)
                    .build();

        } catch (ApiException e) {
            log.error("获取集群资源信息失败: {}", e.getResponseBody(), e);
            throw new RuntimeException("获取集群资源信息失败", e);
        }
    }

    @Override
    public List<NodeInfo> getNodes() {
        try {
            V1NodeList nodeList = coreV1Api.listNode(null, null, null, null, null, null, null, null, null, null);

            return nodeList.getItems().stream()
                    .map(this::convertToNodeInfo)
                    .collect(Collectors.toList());

        } catch (ApiException e) {
            log.error("获取节点信息失败: {}", e.getResponseBody(), e);
            throw new RuntimeException("获取节点信息失败", e);
        }
    }

    // ========== 扩缩容实现 ==========

    @Override
    public boolean scaleDeployment(String deploymentName, int replicas, String namespace) {
        try {
            V1Deployment deployment = appsV1Api.readNamespacedDeployment(
                deploymentName,
                namespace != null ? namespace : defaultNamespace,
                null
            );

            deployment.getSpec().setReplicas(replicas);

            appsV1Api.replaceNamespacedDeployment(
                deploymentName,
                namespace != null ? namespace : defaultNamespace,
                deployment,
                null, null, null, null
            );

            log.info("成功扩缩容 Deployment: {} 到 {} 副本", deploymentName, replicas);
            return true;

        } catch (ApiException e) {
            log.error("扩缩容 Deployment 失败: {}", e.getResponseBody(), e);
            return false;
        }
    }

    // ========== 日志管理实现 ==========

    @Override
    public String getPodLogs(String podName, String namespace) {
        return getPodLogs(podName, namespace, null, false);
    }

    @Override
    public String getContainerLogs(String podName, String containerName, String namespace) {
        try {
            String logs = coreV1Api.readNamespacedPodLog(
                podName,
                namespace != null ? namespace : defaultNamespace,
                containerName,
                null, null, null, null, null, null, null, null
            );

            return logs;

        } catch (ApiException e) {
            log.error("获取容器日志失败: {}", e.getResponseBody(), e);
            return "获取日志失败: " + e.getMessage();
        }
    }

    @Override
    public String getPodLogs(String podName, String namespace, Integer tailLines, Boolean follow) {
        // 参数验证
        if (podName == null || podName.isEmpty()) {
            throw new IllegalArgumentException("podName cannot be null or empty");
        }

        if (coreV1Api == null) {
            throw new IllegalStateException("coreV1Api is not initialized");
        }

        try {
            String logs = coreV1Api.readNamespacedPodLog(
                podName,
                namespace != null ? namespace : defaultNamespace,
                null, // container
                follow != null ? follow : false,
                null, // limitBytes
                null, // pretty
                null, // previous
                null, // sinceSeconds
                tailLines,
                null, // timestamps
                null  // sinceTime
            );

            return logs;

        } catch (ApiException e) {
            log.error("获取 Pod 日志失败: {}", e.getResponseBody(), e);
            return "获取日志失败: " + e.getMessage();
        }
    }


    // ========== 事件监控实现 ==========

    @Override
    public List<K8sEvent> getPodEvents(String podName, String namespace) {
        try {
            CoreV1EventList eventList = coreV1Api.listNamespacedEvent(
                    namespace != null ? namespace : defaultNamespace,
                    null, null, null,
                    "involvedObject.name=" + podName, // fieldSelector
                    null, null, null, null, null, null
            );

            return eventList.getItems().stream()
                    .map(this::convertToK8sEvent)
                    .collect(Collectors.toList());

        } catch (ApiException e) {
            log.error("获取 Pod 事件失败: {}", e.getResponseBody(), e);
            return Collections.emptyList();
        }
    }


    @Override
    public void watchPodStatus(String namespace, Consumer<PodInfo> callback) {
        try {
            Watch<V1Pod> watch = Watch.createWatch(
                apiClient,
                coreV1Api.listNamespacedPodCall(
                    namespace != null ? namespace : defaultNamespace,
                    null, null, null, null, null, null, null, null, null, null, null
                ),
                new com.google.gson.reflect.TypeToken<Watch.Response<V1Pod>>(){}.getType()
            );

            for (Watch.Response<V1Pod> item : watch) {
                PodInfo podInfo = convertToPodInfo(item.object);
                callback.accept(podInfo);
            }

        } catch (Exception e) {
            log.error("监听 Pod 状态失败", e);
        }
    }

    @Override
    public CompletableFuture<Void> watchPodStatusAsync(String namespace, Consumer<PodInfo> callback) {
        return CompletableFuture.runAsync(() -> watchPodStatus(namespace, callback));
    }

    // ========== 健康检查实现 ==========

    @Override
    public boolean isConnected() {
        try {
            coreV1Api.getAPIResources();
            return true;
        } catch (Exception e) {
            log.error("K8s 连接检查失败", e);
            return false;
        }
    }

    @Override
    public String getVersion() {
        try {
            // 获取 K8s 版本信息
            VersionApi versionApi = new VersionApi(apiClient);
            VersionInfo versionInfo = versionApi.getCode();
            if (versionInfo != null) {
                return versionInfo.getGitVersion();
            }
            return "unknown";
        } catch (Exception e) {
            log.error("获取 K8s 版本失败", e);
            return "unknown";
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 转换 V1Pod 到 PodInfo
     */
    private PodInfo convertToPodInfo(V1Pod pod) {
        V1ObjectMeta metadata = pod.getMetadata();
        V1PodStatus status = pod.getStatus();

        List<ContainerInfo> containers = new ArrayList<>();
        if (status.getContainerStatuses() != null) {
            for (V1ContainerStatus containerStatus : status.getContainerStatuses()) {
                containers.add(convertToContainerInfo(containerStatus));
            }
        }

        return PodInfo.builder()
                .name(metadata.getName())
                .namespace(metadata.getNamespace())
                .phase(status.getPhase())
                .podIP(status.getPodIP())
                .nodeName(status.getNodeName())
                .creationTime(convertToLocalDateTime(metadata.getCreationTimestamp()))
                .startTime(convertToLocalDateTime(status.getStartTime()))
                .labels(metadata.getLabels())
                .annotations(metadata.getAnnotations())
                .containers(containers)
                .restartCount(calculateRestartCount(containers))
                .ready(isPodReady(status))
                .build();
    }

    /**
     * 转换 V1ContainerStatus 到 ContainerInfo
     */
    private ContainerInfo convertToContainerInfo(V1ContainerStatus containerStatus) {
        V1ContainerState state = containerStatus.getState();
        String stateStr = "Unknown";
        LocalDateTime startedAt = null;
        LocalDateTime finishedAt = null;
        Integer exitCode = null;
        String reason = null;
        String message = null;

        if (state.getRunning() != null) {
            stateStr = "Running";
            startedAt = convertToLocalDateTime(state.getRunning().getStartedAt());
        } else if (state.getWaiting() != null) {
            stateStr = "Waiting";
            reason = state.getWaiting().getReason();
            message = state.getWaiting().getMessage();
        } else if (state.getTerminated() != null) {
            stateStr = "Terminated";
            startedAt = convertToLocalDateTime(state.getTerminated().getStartedAt());
            finishedAt = convertToLocalDateTime(state.getTerminated().getFinishedAt());
            exitCode = state.getTerminated().getExitCode();
            reason = state.getTerminated().getReason();
            message = state.getTerminated().getMessage();
        }

        return ContainerInfo.builder()
                .name(containerStatus.getName())
                .image(containerStatus.getImage())
                .imageID(containerStatus.getImageID())
                .containerID(containerStatus.getContainerID())
                .state(stateStr)
                .ready(containerStatus.getReady())
                .restartCount(containerStatus.getRestartCount())
                .startedAt(startedAt)
                .finishedAt(finishedAt)
                .exitCode(exitCode)
                .reason(reason)
                .message(message)
                .build();
    }

    /**
     * 转换 V1Node 到 NodeInfo
     */
    private NodeInfo convertToNodeInfo(V1Node node) {
        V1ObjectMeta metadata = node.getMetadata();
        V1NodeStatus status = node.getStatus();

        String internalIP = null;
        String externalIP = null;
        if (status.getAddresses() != null) {
            for (V1NodeAddress address : status.getAddresses()) {
                if ("InternalIP".equals(address.getType())) {
                    internalIP = address.getAddress();
                } else if ("ExternalIP".equals(address.getType())) {
                    externalIP = address.getAddress();
                }
            }
        }

        boolean ready = false;
        if (status.getConditions() != null) {
            ready = status.getConditions().stream()
                    .anyMatch(condition -> "Ready".equals(condition.getType()) &&
                             "True".equals(condition.getStatus()));
        }

        // 获取资源容量
        Map<String, io.kubernetes.client.custom.Quantity> capacity = status.getCapacity();
        long cpuCapacity = parseQuantityToLong(capacity.get("cpu"), 1000); // 转换为 millicores
        long memoryCapacity = parseQuantityToLong(capacity.get("memory"), 1);
        long storageCapacity = parseQuantityToLong(capacity.get("ephemeral-storage"), 1);
        int podCapacity = parseQuantityToInt(capacity.get("pods"));

        return NodeInfo.builder()
                .name(metadata.getName())
                .status(ready ? "Ready" : "NotReady")
                .ready(ready)
                .internalIP(internalIP)
                .externalIP(externalIP)
                .osImage(status.getNodeInfo() != null ? status.getNodeInfo().getOsImage() : null)
                .kernelVersion(status.getNodeInfo() != null ? status.getNodeInfo().getKernelVersion() : null)
                .containerRuntimeVersion(status.getNodeInfo() != null ? status.getNodeInfo().getContainerRuntimeVersion() : null)
                .kubeletVersion(status.getNodeInfo() != null ? status.getNodeInfo().getKubeletVersion() : null)
                .creationTime(convertToLocalDateTime(metadata.getCreationTimestamp()))
                .cpuCapacity(cpuCapacity)
                .memoryCapacity(memoryCapacity)
                .storageCapacity(storageCapacity)
                .podCapacity(podCapacity)
                .labels(metadata.getLabels())
                .annotations(metadata.getAnnotations())
                .build();
    }

    /**
     * 转换 CoreV1Event 到 K8sEvent
     */
    private K8sEvent convertToK8sEvent(CoreV1Event event) {
        V1ObjectMeta metadata = event.getMetadata();
        V1ObjectReference involvedObject = event.getInvolvedObject();
        V1EventSource source = event.getSource();

        return K8sEvent.builder()
                .name(metadata != null ? metadata.getName() : null)
                .namespace(metadata != null ? metadata.getNamespace() : null)
                .type(event.getType())
                .reason(event.getReason())
                .message(event.getMessage())
                .involvedObjectKind(involvedObject != null ? involvedObject.getKind() : null)
                .involvedObjectName(involvedObject != null ? involvedObject.getName() : null)
                .involvedObjectNamespace(involvedObject != null ? involvedObject.getNamespace() : null)
                .sourceComponent(source != null ? source.getComponent() : null)
                .sourceHost(source != null ? source.getHost() : null)
                .firstTimestamp(convertToLocalDateTime(event.getFirstTimestamp()))
                .lastTimestamp(convertToLocalDateTime(event.getLastTimestamp()))
                .count(event.getCount())
                .reportingComponent(event.getReportingComponent())
                .reportingInstance(event.getReportingInstance())
                .build();
    }

    /**
     * 构建 V1Pod 对象
     */
    private V1Pod buildV1Pod(PodSpec podSpec) {
        V1Pod pod = new V1Pod();

        // 设置 metadata
        V1ObjectMeta metadata = new V1ObjectMeta();
        metadata.setName(podSpec.getName());
        metadata.setNamespace(podSpec.getNamespace());
        metadata.setLabels(podSpec.getLabels());
        metadata.setAnnotations(podSpec.getAnnotations());
        pod.setMetadata(metadata);

        // 设置 spec
        V1PodSpec spec = new V1PodSpec();
        spec.setRestartPolicy(podSpec.getRestartPolicy() != null ? podSpec.getRestartPolicy() : "Always");
        spec.setNodeSelector(podSpec.getNodeSelector());
        spec.setServiceAccountName(podSpec.getServiceAccountName());
        spec.setDnsPolicy(podSpec.getDnsPolicy());
        spec.setHostNetwork(podSpec.getHostNetwork());

        // 设置容器
        if (podSpec.getContainers() != null) {
            List<V1Container> containers = podSpec.getContainers().stream()
                    .map(this::buildV1Container)
                    .collect(Collectors.toList());
            spec.setContainers(containers);
        }

        pod.setSpec(spec);
        return pod;
    }

    /**
     * 构建 V1Container 对象
     */
    private V1Container buildV1Container(ContainerSpec containerSpec) {
        V1Container container = new V1Container();
        container.setName(containerSpec.getName());
        container.setImage(containerSpec.getImage());
        container.setImagePullPolicy(containerSpec.getImagePullPolicy());
        container.setCommand(containerSpec.getCommand());
        container.setArgs(containerSpec.getArgs());
        container.setWorkingDir(containerSpec.getWorkingDir());

        // 设置环境变量
        if (containerSpec.getEnv() != null) {
            List<V1EnvVar> envVars = containerSpec.getEnv().entrySet().stream()
                    .map(entry -> new V1EnvVar().name(entry.getKey()).value(entry.getValue()))
                    .collect(Collectors.toList());
            container.setEnv(envVars);
        }

        // 设置资源要求
        if (containerSpec.getResources() != null) {
            V1ResourceRequirements resources = buildV1ResourceRequirements(containerSpec.getResources());
            container.setResources(resources);
        }

        return container;
    }

    /**
     * 构建 V1ResourceRequirements 对象
     */
    private V1ResourceRequirements buildV1ResourceRequirements(ResourceRequirements resources) {
        V1ResourceRequirements requirements = new V1ResourceRequirements();

        Map<String, io.kubernetes.client.custom.Quantity> requests = new HashMap<>();
        Map<String, io.kubernetes.client.custom.Quantity> limits = new HashMap<>();

        if (resources.getCpuRequest() != null) {
            requests.put("cpu", io.kubernetes.client.custom.Quantity.fromString(resources.getCpuRequest() + "m"));
        }
        if (resources.getMemoryRequest() != null) {
            requests.put("memory", io.kubernetes.client.custom.Quantity.fromString(resources.getMemoryRequest().toString()));
        }
        if (resources.getCpuLimit() != null) {
            limits.put("cpu", io.kubernetes.client.custom.Quantity.fromString(resources.getCpuLimit() + "m"));
        }
        if (resources.getMemoryLimit() != null) {
            limits.put("memory", io.kubernetes.client.custom.Quantity.fromString(resources.getMemoryLimit().toString()));
        }

        requirements.setRequests(requests);
        requirements.setLimits(limits);

        return requirements;
    }

    /**
     * 转换时间戳到 LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(java.time.OffsetDateTime offsetDateTime) {
        if (offsetDateTime == null) {
            return null;
        }
        return offsetDateTime.toLocalDateTime();
    }

    /**
     * 解析 Quantity 到 long
     */
    private long parseQuantityToLong(io.kubernetes.client.custom.Quantity quantity, long multiplier) {
        if (quantity == null) {
            return 0L;
        }
        try {
            // 使用 Quantity 的 getNumber() 方法获取数值
            java.math.BigDecimal number = quantity.getNumber();
            return number.longValue() * multiplier;
        } catch (Exception e) {
            log.warn("解析 Quantity 失败: {}", quantity, e);
            return 0L;
        }
    }

    /**
     * 解析 Quantity 到 int
     */
    private int parseQuantityToInt(io.kubernetes.client.custom.Quantity quantity) {
        if (quantity == null) {
            return 0;
        }
        try {
            java.math.BigDecimal number = quantity.getNumber();
            return number.intValue();
        } catch (Exception e) {
            log.warn("解析 Quantity 失败: {}", quantity, e);
            return 0;
        }
    }

    /**
     * 计算重启次数
     */
    private Integer calculateRestartCount(List<ContainerInfo> containers) {
        return containers.stream()
                .mapToInt(container -> container.getRestartCount() != null ? container.getRestartCount() : 0)
                .sum();
    }

    /**
     * 检查 Pod 是否就绪
     */
    private Boolean isPodReady(V1PodStatus status) {
        if (status.getConditions() == null) {
            return false;
        }

        return status.getConditions().stream()
                .anyMatch(condition -> "Ready".equals(condition.getType()) &&
                         "True".equals(condition.getStatus()));
    }

    /**
     * 调度清理任务
     */
    private void scheduleCleanup(String podName, String namespace, Long delaySeconds) {
        if (delaySeconds == null || delaySeconds <= 0) {
            delaySeconds = 300L; // 默认 5 分钟后清理
        }

        cleanupExecutor.schedule(() -> {
            try {
                deletePod(podName, namespace);
                log.info("自动清理 Pod 完成: {}", podName);
            } catch (Exception e) {
                log.error("自动清理 Pod 失败: {}", podName, e);
            }
        }, delaySeconds, TimeUnit.SECONDS);
    }
}
