package com.sac.job.modules.dispather.service.impl;

import com.sac.job.modules.dispather.service.DispatcherService;
import com.sac.job.modules.dispather.service.JobQueueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DispatcherServiceImpl implements DispatcherService {

    @Autowired
    JobQueueService jobQueueService;

    @Override
    public void execProject(String projectId) {
        jobQueueService.enqueue(projectId);
    }
}
