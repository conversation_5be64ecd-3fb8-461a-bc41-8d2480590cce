package com.sac.job.modules.dispather.service.impl;

import com.sac.job.job.bo.JobK8sConfigBO;
import com.sac.job.modules.dispather.service.JobQueueConsumerService;
import com.sac.job.modules.dispather.service.JobQueueService;
import com.sac.job.modules.dispather.service.K8sService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class JobQueueConsumerServiceImpl implements JobQueueConsumerService {

    @Autowired
    JobQueueService jobQueueService;

    @Autowired
    K8sService k8sService;

    @PostConstruct
    private void consumeQueue() {
        new Thread(this::run);
    }

    private void run() {
        try {
            throwableRun();
        } catch (Throwable e) {
//            log.error("{}", e.getMessage());
        }
    }

    private void throwableRun() throws Throwable {
        while (true) {
            if (k8sService.notEnoughResources()) {
                TimeUnit.SECONDS.sleep(1);
            } else {
                String jobId = jobQueueService.dequeue();
                JobK8sConfigBO config = queryJobK8sConfig(jobId);
                k8sService.run(config);
            }
        }
    }

    private JobK8sConfigBO queryJobK8sConfig(String jobId) {
        throw new UnsupportedOperationException();
    }

}
