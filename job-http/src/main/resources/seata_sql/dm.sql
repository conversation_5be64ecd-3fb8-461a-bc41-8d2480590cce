CREATE TABLE "sac_business"."UNDO_LOG"
(
"ID" BIGINT IDENTITY(1, 1) NOT NULL,
"BRANCH_ID" BIGINT NOT NULL,
"XID" VARCHAR2(100) NOT NULL,
"CONTEXT" VARCHAR2(500) NOT NULL,
"R<PERSON><PERSON><PERSON><PERSON><PERSON>_INFO" BLOB NOT NULL,
"LOG_STATUS" INT NOT NULL,
"LOG_CREATED" DATETIME(6) NOT NULL,
"LOG_MODIFIED" DATETIME(6) NOT NULL,
NOT CLUSTER PRIMARY KEY("ID"),
CONSTRAINT "UX_UNDO_LOG" UNIQUE("XID", "<PERSON><PERSON>CH_ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

