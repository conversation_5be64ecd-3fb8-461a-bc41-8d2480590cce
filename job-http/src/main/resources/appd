#!/bin/bash
# chkconfig: 2345 70 80
# description: app script ....

# this script move to /etc/init.d

# must set variable script_home/script_username/script_memory
script_home=/home/<USER>/eureka
script_memory=512M
script_username=tomcat

# 信号编号SIGTERM，程序会清理线程现场，完成之后再结束程序
# 如果程序还有事务在处理，是不会马上结束程序的，会执行Runtime.getRuntime().addShutdownHook(*)
script=$script_home/app.sh

# See how we were called.
case "$1" in
  start)
        su $script_username -l -c "sh $script -h $script_home -m $script_memory -c $1"
        ;;
  stop)
        su $script_username -l -c "sh $script -h $script_home -c $1"
        ;;
  restart|reload)
        stop
        start
        ;;
  *)
        echo $"Usage: $0 {start|stop|restart|reload}"
        exit 1
esac

exit

