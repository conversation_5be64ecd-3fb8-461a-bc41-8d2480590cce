<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sac.job.modules.project.mapper">

    <!--示例mybatis-plus xml 示例-->
    <select id="all" resultType="com.sac.job.job.entity.Project" parameterType="java.lang.String">
        SELECT *
        FROM T_B_PROJECT
        where condition = #{condition}
    </select>

</mapper>