server:
  port: 15360

dispatch:
  open: false

xxl:
  job:
    admin:
      addresses:
    executor:
      address:
      appname: sac-Job
      ip:
      port: 19998
      logpath: /data/xxl-job/jobhandler
      logretentiondays: 30
    accessToken:

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      primary:
        url: jdbc:dm://*************:5236?schema=sac_business
        username: SYSDBA
        password: SYSDBA
        driver-class-name: dm.jdbc.driver.DmDriver
      second:
        url: jdbc:dm://*************:5236?schema=sac_business
        username: SYSDBA
        password: SYSDBA
        driver-class-name: dm.jdbc.driver.DmDriver
      third:
        url: jdbc:dm://*************:5236?schema=sac_business
        username: SYSDBA
        password: SYSDBA
        driver-class-name: dm.jdbc.driver.DmDriver

      initial-size: 5
      max-active: 5
      min-idle: 5
      filters: config,stat,slf4j
      connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIMvNjcgKPDsgUXNR8Rs9BqZET+5p3hxcoiSrcx7sv09joUcrNnYkvR7JKa+SS/Y9Je+P5Gs0dKzyFPosEM49PcCAwEAAQ==
      filter.config.enabled: true
      test-on-borrow: true
      test-on-return: true
      test-while-idle: true