# K8s 配置
k8s:
  # 是否启用 K8s 功能
  enabled: true
  
  config:
    # 是否在集群内运行（使用 ServiceAccount）
    in-cluster: false
    
    # kubeconfig 文件路径（集群外运行时使用）
    kubeconfig: ${K8S_KUBECONFIG_PATH:~/.kube/config}
    
    # K8s API Server 地址（可选）
    master-url: ${K8S_MASTER_URL:}
    
    # 默认命名空间
    namespace: ${K8S_DEFAULT_NAMESPACE:default}
    
    # 连接超时时间（毫秒）
    connection-timeout: ${K8S_CONNECTION_TIMEOUT:30000}
    
    # 读取超时时间（毫秒）
    read-timeout: ${K8S_READ_TIMEOUT:60000}
  
  monitoring:
    # 状态检查间隔（秒）
    check-interval: ${K8S_CHECK_INTERVAL:30}
    
    # 资源监控间隔（秒）
    resource-check-interval: ${K8S_RESOURCE_CHECK_INTERVAL:60}
    
    # 是否启用资源监控
    resource-monitoring-enabled: ${K8S_RESOURCE_MONITORING_ENABLED:true}
  
  job:
    # 默认作业超时时间（秒）
    default-timeout: ${K8S_JOB_DEFAULT_TIMEOUT:3600}
    
    # 默认重试次数
    default-backoff-limit: ${K8S_JOB_DEFAULT_BACKOFF_LIMIT:3}
    
    # 是否自动清理完成的作业
    auto-cleanup: ${K8S_JOB_AUTO_CLEANUP:true}
    
    # 清理延迟时间（秒）
    cleanup-delay: ${K8S_JOB_CLEANUP_DELAY:300}
  
  resources:
    # 资源不足阈值
    cpu-threshold: ${K8S_CPU_THRESHOLD:80}
    memory-threshold: ${K8S_MEMORY_THRESHOLD:80}
    
    # 默认资源请求
    default-cpu-request: ${K8S_DEFAULT_CPU_REQUEST:100m}
    default-memory-request: ${K8S_DEFAULT_MEMORY_REQUEST:128Mi}
    
    # 默认资源限制
    default-cpu-limit: ${K8S_DEFAULT_CPU_LIMIT:500m}
    default-memory-limit: ${K8S_DEFAULT_MEMORY_LIMIT:512Mi}
