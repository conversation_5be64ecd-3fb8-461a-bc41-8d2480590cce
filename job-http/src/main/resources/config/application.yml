spring:
  application:
    name: sac-job-provider
  output:
    ansi:
      enabled: always
  main:
    allow-bean-definition-overriding: true
  profiles:
    #分为开发环境dev、测试环境test和生产环境prod
    active: dev
    include: gateway-base,seata
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

app:
  id: ${spring.application.name}
  version: @revision@

# Mybatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapping/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  #typeAliasesPackage: ${package}.${application}.module
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 3
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
    refresh-mapper: true
    #数据库大写下划线转换
    #capital-mode: true
    #序列接口实现类配置
    #key-generator: com.baomidou.springboot.xxx
    #逻辑删除配置
    logic-delete-value: -1
    logic-not-delete-value: 0
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.xxx
    #自定义SQL注入器
    #sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  config: classpath:config/log4j2.yml

# Swagger配置
swagger:
  enable: true
  title: XXX服务
  description: XXX服务接口文档说明
  terms-of-service-url : http://www.sac-china.com/
  contact-name: 华盾公司
  contact-url: http://www.sac-china.com/
  contact-email: <联系邮箱>
  version: v1
  base-package: com.sac.job.controller
  docs: v2/api-docs
  string-extensions:
    - name: apiPrefix
      value: /sac/job/v3

management:
  endpoints:
    web:
      base-path: /
      exposure:
        include: '*'
  metrics:
    tags:
      application: ${spring.application.name}
    export:
      prometheus:
        enabled: true
        step: 1ms
        descriptions: true
  endpoint:
    health:
      enabled: true
      show-details: always
    prometheus:
      enabled: true
    env:
      enabled: true
    mappings:
      enabled: true
    beans:
      enabled: true
  health:
    redis:
      enabled: true

authorization:
  enable: ${AUTHORIZATION_ENABLE:false}
  default-client: ${AUTHORIZATION_DEFAULT_CLIENT:ibps}
  default-secret: ${AUTHORIZATION_DEFAULT_SECRET:58b65297-3467-0859-8337-8cbaf81ef68a}
  default-username: ${AUTHORIZATION_DEFAULT_USERNAME:admin}
  default-password: ${AUTHORIZATION_DEFAULT_PASSWORD:1qazXSW@CDE#}
  validator-filter-enable: false
  permission-filter-enable: false
  generator-filter-enable: false

#client:
#  gateway:
#    disable: ${CLIENT_GATEWAY_DISABLE:true}
#    ignoreUrls:
#      - /health
#      - /favicon.ico
#      - /cat/s/router
#      - /actuator/prometheus

#tp:
#  monitor:
#    enabled: true















