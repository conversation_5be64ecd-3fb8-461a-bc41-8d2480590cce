feign:
  options:
    readTimeout: 60000
    connectTimeout: 30000
  httpclient:
    enabled: false
    disable-ssl-validation: true
  okhttp:
    enabled: true
  hystrix:
    enabled: false

hystrix:
  threadpool:
    # 服务名，填写default为所有服务
    default:
      allowMaximumSizeToDivergeFromCoreSize: true
      coreSize: ${HYSTRIX_THREADPOOL_DEFAULT_CORESIZE_GW:128}
      maximumSize: ${HYSTRIX_THREADPOOL_DEFAULT_MAXIMUMSIZE_GW:256}
  command:
    # 服务名，填写default为所有服务
    default:
      circuitBreaker:
        requestVolumeThreshold: 1      # 在给定的时间范围内(统计时间窗：默认10s metrics.rollingStats.timeInMilliseconds)，方法应该被调用的次数
        sleepWindowInMilliseconds: 5000   # 处于打开状态的断路器要经过多长时间才会进入半开状态，进入半开状态之后，将会再次尝试失败的原始方法。
        errorThresholdPercentage: 50      # 在给定的时间范围内(默认：10s)，方法调用产生失败的百分比。
      fallback:
        isolation:
          semaphore:
            maxConcurrentRequests: 100    # 该属性用来设置从调用线程中允许HystrixCommand.getFallback()方法执行的最大并发请求数。当达到最大并发请求时，后续的请求将会被拒绝并抛出异常。
      execution:
        timeout:
          enabled: true                   # 该属性用来配置HystrixCommand.run()的执行是否启用超时时间。默认为true
        isolation:
          thread:
            timeoutInMilliseconds: 1000  # 该属性用来配置HystrixCommand执行的超时时间，单位为毫秒。

# Ribbon 总时间ribbonTimeout = (ribbonReadTimeout + ribbonConnectTimeout) * (maxAutoRetries + 1) * (maxAutoRetriesNextServer + 1);
# ribbon全局设置
ribbon:
  eureka:
    # 会影响eureka注册时负载，使用eureka注册时必须设置true
    enabled: true
  httpclient:
    enabled: false
    disable-ssl-validation: true
  okhttp:
    enabled: true
  # 请求负载的超时时间
  ReadTimeout: 15000
  # 请求连接的超时时间
  ConnectTimeout: 15000
  # 对当前实例的重试次数
  MaxAutoRetries: 0
  # 切换实例的重试次数
  MaxAutoRetriesNextServer: 1
  # 是否所有操作都重试
  OkToRetryOnAllOperations: false

