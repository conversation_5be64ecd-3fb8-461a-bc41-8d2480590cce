<?xml version="1.0" encoding="UTF-8"?>
<!-- debug为true时，将打印出logback内部日志信息，实时查看logback运行状态 -->
<configuration scan="true" sacnPeriod="600 seconds" debug="false">
    <property name="APP_NAME" value="job" />
    <property name="ROOT_LEVEL" value="ERROR"></property>
    <property name="MAX_HISTORY" value="30" />
    <property name="TOTAL_SIZECAP" value="500MB" />
    <property name="ERROR_TOTAL_SIZECAP" value="200MB" />
    <!-- 定义日志文件的存储路径，勿在LogBack的配置中使用相对路径,首先查找系统属性-Dlog.dir，如果存在就用它，否则在当前目录下创建名为logs目录做日志存放的目录 -->
    <property name="LOG_HOME" value="${log.dir:-logs}/${APP_NAME}" />
    <property name="LOGFILE_MAXSIZE" value="10MB" />
    <timestamp key="BY_SECOND" datePattern="yyyy MM dd HH:mm:ss" />
    <contextName>${APP_NAME}</contextName>
    <property name="CONSOLE_PATTERN" value="%-4relative [%thread] %-5level %logger{35} %L - %msg %n" />
    <property name="FILE_PATTERN" value="%d{${BY_SECOND}} %-4relative [%thread] %-5level %logger{35} %L - %msg %n" />

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!--<encoding>UTF-8</encoding>-->
        <encoder>
            <pattern>${CONSOLE_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/log.log</file>
        <!--<encoding>UTF-8</encoding>-->
        <!--<Encoding>UTF-8</Encoding>-->
        <append>true</append>
        <!-- support multiple-JVM writing to the same log file -->
        <prudent>true</prudent>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_PATTERN}</pattern>
        </encoder>
        <!-- 配置滚动策略 ch.qos.logback.core.rolling.TimeBasedRollingPolicy-->
        <RollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志名称的格式,ww每周回滚 -->
            <fileNamePattern>${LOG_HOME}/allArchived/log.%d{yyyy-ww}.%i.log</fileNamePattern>
            <!-- 保存最长时间：天数 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZECAP}</totalSizeCap>
            <maxFileSize>${LOGFILE_MAXSIZE}</maxFileSize>
        </RollingPolicy>
    </appender>

    <appender name="FILE_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE" />
    </appender>

    <appender name="FILE.ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error.log</file>
        <!--<encoding>UTF-8</encoding>-->
        <append>true</append>
        <prudent>true</prudent>
        <RollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/archived/error.%d{yyyy-ww}.%i.log</FileNamePattern>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${ERROR_TOTAL_SIZECAP}</totalSizeCap>
            <maxFileSize>${LOGFILE_MAXSIZE}</maxFileSize>
        </RollingPolicy>
        <encoder>
            <pattern>${FILE_PATTERN}</pattern>
        </encoder>
        <!-- 只输出高于WARN级别日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <logger name="org.springframework" level="ERROR"/>
    <logger name="springfox" level="ERROR"></logger>
    <root level="${ROOT_LEVEL}">
        <!-- <appender-ref ref="FILE" /> -->
        <appender-ref ref="FILE.ERROR" />
        <appender-ref ref="FILE_ASYNC" />
    </root>
    <root level="DEBUG">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>