apollo:
  # meta: ${APOLLO_META:http://config-service-url}
  bootstrap:
    enabled: false
    eagerLoad:
      enabled: false
    namespaces: application-dev-base.yml,application-dev-consul.yml,sac-job-provider.yml
    order: 1
app:
  id: sac-platform # 与 Apollo 配置中心中的 AppId 一致
env: DEV # 指定环境

spring:
  cloud:
    consul:
      enabled: false
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:nacos.sac-software:8848}
        # 填写命名空间ID！！！
        namespace: ${NACOS_NAMESPACE:029df0a9-8e5a-4b18-a210-da4be2ed5949}
        group: ${NACOS_GROUP:platform_group}
        service: ${spring.application.name}
        enabled: true
      config:
        enabled: true
        server-addr: ${NACOS_CONFIG_SERVER_ADDR:nacos.sac-software:8848}
        namespace: ${NACOS_CONFIG_NAMESPACE:029df0a9-8e5a-4b18-a210-da4be2ed5949}
        group: ${NACOS_GROUP:job_group}
        file-extension: yml
        username: nacos
        password: nacos
