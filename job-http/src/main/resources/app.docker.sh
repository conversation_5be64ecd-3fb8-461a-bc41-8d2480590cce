#!/bin/bash

show_usage="args: [-h, -m, -n, -s, -r, -c]\
                                  [--home, --memory=, --nmemory=, --smemory=, --ratio=, --command]"

p_home=$(cd `dirname $0`; pwd)
p_memory="4g"
n_memory="3g"
s_memory="512k"
p_ratio="8"
p_command="start"
agent_home=${p_home}/..

GETOPT_ARGS=`getopt -o h:m:n:s:c: -al home:,memory:,nmemory:,smemory:,ratio:,command: -- "$@"`
eval set -- "$GETOPT_ARGS"

while [ -n "$1" ]
do
        case "$1" in
		-h|--home) p_home=$2; shift 2;;
                -m|--memory) p_memory=$2; shift 2;;
                -n|--nmemory) n_memory=$2; shift 2;;
                -n|--smemory) s_memory=$2; shift 2;;
                -r|--ratio) n_ratio=$2; shift 2;;
                -c|--command) p_command=$2; shift 2;;
                --) break ;;
                *) echo $1,$2,$show_usage; break ;;
        esac
done

p_jar=`ls ${p_home} | grep '.jar$'`
md=`echo ${p_jar} | sed 's/\.jar//g'`

start() {
	echo "---------------------------------------------"
	echo "--------------$md starting----------------"
	echo "---------------------------------------------"

    if [ ! -f "$SPRING_PROFILES_ACTIVE" ];then
		active=$SPRING_PROFILES_ACTIVE
	else
		active=`cat ${p_home}/config/application.yml | grep active | sed 's/active://g' | awk '{gsub(/^\s+|\s+$/, "");print}' | sed 's/SPRING_PROFILES_ACTIVE://g' | sed 's/${//g' | sed 's/}//g'`
	fi
	cmdline=${p_home}/config/cmdline-${active}.txt
	echo "info: -- cmdline -- ${cmdline}"

	javaagent=''
	if [ ! -d "${agent_home}/agent" ];then
		echo "info: Skywalking agent is not exsits."
	elif [ `ls -A ${agent_home}/agent | wc -l` = "0" ];then
		echo "info: Skywalking agent is empty."
	else
	    options=`cat ${cmdline}`
		javaagent="-javaagent:${agent_home}/agent/skywalking-agent.jar${options}"
	fi
	
	IBPS_JVM_OPTS="-XX:SurvivorRatio=${p_ratio} -Xms${p_memory} -Xmx${p_memory} -Xmn${n_memory} -Xss${s_memory} -XX:+UseParNewGC -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:LargePageSizeInBytes=64M -XX:+UseFastAccessorMethods";
	if [ -n "${ENV_IBPS_JVM_OPTS}" ] ; then
	    IBPS_JVM_OPTS=${ENV_IBPS_JVM_OPTS}
	fi

	IBPS_JVM_LOG_OPTS="-Dloggers.level=info -Dlog.level.console=info -Dlog.level.appender=info";
  if [ -n "${ENV_IBPS_JVM_LOG_OPTS}" ] ; then
  	  IBPS_JVM_LOG_OPTS=${ENV_IBPS_JVM_LOG_OPTS}
  fi
  
  # -Djava.rmi.server.hostname=************ -Dcom.sun.management.jmxremote.port=8099 -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.ssl=false -Djava.net.preferIPv4Stack=true -Dcom.sun.management.jmxremote.authenticate=false
	IBPS_JVM_OTHER_OPTS="";
  if [ -n "${ENV_IBPS_JVM_OTHER_OPTS}" ] ; then
  	  IBPS_JVM_OTHER_OPTS=${ENV_IBPS_JVM_OTHER_OPTS}
  fi

	java -Djava.awt.headless=true \
		-Dfile.encoding=UTF-8 \
		-Duser.timezone=GMT+8 \
		-Dproject.host.name=`hostname` \
		${IBPS_JVM_LOG_OPTS} \
		${IBPS_JVM_OTHER_OPTS} \
		${IBPS_JVM_OPTS} \
		${javaagent} \
		-jar ${p_home}/${p_jar}
}

stop() {
	echo "info: ---------------------------------------------"
	echo "info: --------------$md stoping----------------"
	echo "info: ---------------------------------------------"

	if [ ! -f "$p_home/pid" ];then
		echo "info: -- $md is not started."
	else
		pid=$(cat $p_home/pid)
		kill -15 ${pid}
	fi
}

# See how we were called.
case "$p_command" in
  start)
        start
        ;;
  stop)
        stop
        ;;
  restart|reload)
        stop
        start
        ;;
  *)
        echo $"Usage: $0 {start|stop|restart|reload}"
        exit 1
esac

exit
