package com.sac.job.controller;

import com.sac.framework.response.BaseResponse;
import com.sac.job.k8s.bo.*;
import com.sac.job.modules.dispather.service.K8sService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * K8s 管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/k8s")
@Api(tags = "K8s 管理")
@ConditionalOnProperty(prefix = "k8s", name = "enabled", havingValue = "true", matchIfMissing = false)
public class K8sController {
    
    @Autowired
    private K8sService k8sService;
    
    /**
     * 获取集群资源信息
     */
    @GetMapping("/cluster/resources")
    @ApiOperation("获取集群资源信息")
    public BaseResponse<ClusterResourceInfo> getClusterResources() {
        try {
            ClusterResourceInfo resourceInfo = k8sService.getClusterResources();
            return BaseResponse.<ClusterResourceInfo>builder()
                    .data(resourceInfo)
                    .message("获取集群资源信息成功")
                    .build();
        } catch (Exception e) {
            log.error("获取集群资源信息失败", e);
            return BaseResponse.<ClusterResourceInfo>builder()
                    .state(500)
                    .message("获取集群资源信息失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 获取节点列表
     */
    @GetMapping("/nodes")
    @ApiOperation("获取节点列表")
    public BaseResponse<List<NodeInfo>> getNodes() {
        try {
            List<NodeInfo> nodes = k8sService.getNodes();
            return BaseResponse.<List<NodeInfo>>builder()
                    .data(nodes)
                    .message("获取节点列表成功")
                    .build();
        } catch (Exception e) {
            log.error("获取节点列表失败", e);
            return BaseResponse.<List<NodeInfo>>builder()
                    .state(500)
                    .message("获取节点列表失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 获取 Pod 列表
     */
    @GetMapping("/pods")
    @ApiOperation("获取 Pod 列表")
    public BaseResponse<List<PodInfo>> getPods(
            @ApiParam("命名空间") @RequestParam(required = false) String namespace) {
        try {
            List<PodInfo> pods = namespace != null ? 
                    k8sService.listPods(namespace) : 
                    k8sService.listAllPods();
            return BaseResponse.<List<PodInfo>>builder()
                    .data(pods)
                    .message("获取 Pod 列表成功")
                    .build();
        } catch (Exception e) {
            log.error("获取 Pod 列表失败", e);
            return BaseResponse.<List<PodInfo>>builder()
                    .state(500)
                    .message("获取 Pod 列表失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 获取 Pod 详细信息
     */
    @GetMapping("/pods/{namespace}/{podName}")
    @ApiOperation("获取 Pod 详细信息")
    public BaseResponse<PodInfo> getPodInfo(
            @ApiParam("命名空间") @PathVariable String namespace,
            @ApiParam("Pod 名称") @PathVariable String podName) {
        try {
            PodInfo podInfo = k8sService.getPodInfo(podName, namespace);
            if (podInfo == null) {
                return BaseResponse.<PodInfo>builder()
                        .state(404)
                        .message("Pod 不存在")
                        .build();
            }
            return BaseResponse.<PodInfo>builder()
                    .data(podInfo)
                    .message("获取 Pod 信息成功")
                    .build();
        } catch (Exception e) {
            log.error("获取 Pod 信息失败", e);
            return BaseResponse.<PodInfo>builder()
                    .state(500)
                    .message("获取 Pod 信息失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 获取 Pod 日志
     */
    @GetMapping("/pods/{namespace}/{podName}/logs")
    @ApiOperation("获取 Pod 日志")
    public BaseResponse<String> getPodLogs(
            @ApiParam("命名空间") @PathVariable String namespace,
            @ApiParam("Pod 名称") @PathVariable String podName,
            @ApiParam("容器名称") @RequestParam(required = false) String containerName,
            @ApiParam("尾部行数") @RequestParam(required = false) Integer tailLines) {
        try {
            String logs;
            if (containerName != null) {
                logs = k8sService.getContainerLogs(podName, containerName, namespace);
            } else {
                logs = k8sService.getPodLogs(podName, namespace, tailLines, false);
            }
            return BaseResponse.<String>builder()
                    .data(logs)
                    .message("获取日志成功")
                    .build();
        } catch (Exception e) {
            log.error("获取 Pod 日志失败", e);
            return BaseResponse.<String>builder()
                    .state(500)
                    .message("获取日志失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 获取 Pod 事件
     */
    @GetMapping("/pods/{namespace}/{podName}/events")
    @ApiOperation("获取 Pod 事件")
    public BaseResponse<List<K8sEvent>> getPodEvents(
            @ApiParam("命名空间") @PathVariable String namespace,
            @ApiParam("Pod 名称") @PathVariable String podName) {
        try {
            List<K8sEvent> events = k8sService.getPodEvents(podName, namespace);
            return BaseResponse.<List<K8sEvent>>builder()
                    .data(events)
                    .message("获取事件成功")
                    .build();
        } catch (Exception e) {
            log.error("获取 Pod 事件失败", e);
            return BaseResponse.<List<K8sEvent>>builder()
                    .state(500)
                    .message("获取事件失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 创建 Pod
     */
    @PostMapping("/pods")
    @ApiOperation("创建 Pod")
    public BaseResponse<String> createPod(@RequestBody PodSpec podSpec) {
        try {
            String podName = k8sService.createPod(podSpec, podSpec.getNamespace());
            return BaseResponse.<String>builder()
                    .data(podName)
                    .message("创建 Pod 成功")
                    .build();
        } catch (Exception e) {
            log.error("创建 Pod 失败", e);
            return BaseResponse.<String>builder()
                    .state(500)
                    .message("创建 Pod 失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 删除 Pod
     */
    @DeleteMapping("/pods/{namespace}/{podName}")
    @ApiOperation("删除 Pod")
    public BaseResponse<Boolean> deletePod(
            @ApiParam("命名空间") @PathVariable String namespace,
            @ApiParam("Pod 名称") @PathVariable String podName) {
        try {
            boolean success = k8sService.deletePod(podName, namespace);
            return BaseResponse.<Boolean>builder()
                    .data(success)
                    .message(success ? "删除 Pod 成功" : "删除 Pod 失败")
                    .build();
        } catch (Exception e) {
            log.error("删除 Pod 失败", e);
            return BaseResponse.<Boolean>builder()
                    .state(500)
                    .message("删除 Pod 失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 扩缩容 Deployment
     */
    @PutMapping("/deployments/{namespace}/{deploymentName}/scale")
    @ApiOperation("扩缩容 Deployment")
    public BaseResponse<Boolean> scaleDeployment(
            @ApiParam("命名空间") @PathVariable String namespace,
            @ApiParam("Deployment 名称") @PathVariable String deploymentName,
            @ApiParam("副本数") @RequestParam Integer replicas) {
        try {
            boolean success = k8sService.scaleDeployment(deploymentName, replicas, namespace);
            return BaseResponse.<Boolean>builder()
                    .data(success)
                    .message(success ? "扩缩容成功" : "扩缩容失败")
                    .build();
        } catch (Exception e) {
            log.error("扩缩容 Deployment 失败", e);
            return BaseResponse.<Boolean>builder()
                    .state(500)
                    .message("扩缩容失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 检查连接状态
     */
    @GetMapping("/health")
    @ApiOperation("检查 K8s 连接状态")
    public BaseResponse<Boolean> checkHealth() {
        try {
            boolean connected = k8sService.isConnected();
            return BaseResponse.<Boolean>builder()
                    .data(connected)
                    .message(connected ? "连接正常" : "连接异常")
                    .build();
        } catch (Exception e) {
            log.error("检查连接状态失败", e);
            return BaseResponse.<Boolean>builder()
                    .state(500)
                    .data(false)
                    .message("检查连接状态失败")
                    .cause(e.getMessage())
                    .build();
        }
    }
}
