package com.sac.job.controller;

import com.lc.ibps.api.base.constants.StateEnum;
import com.sac.job.constants.HttpConstants;
import com.sac.job.modules.idempotent.service.IdempotentService;
import com.sac.framework.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(HttpConstants.REST_URL_PREFIX + "/idempotent")
@Api(value = "IdempotentController", tags = "幂等性接口")
public class IdempotentController {

    @Autowired
    private IdempotentService idempotentService;

    /**
     * 查询所有项目(xm2)
     *
     * @return
     */
    @ApiOperation(value = "查询项目列表", httpMethod = "GET")
    @GetMapping("/createToken")
    public BaseResponse<String> createToken() {
        String token = idempotentService.createToken();
        BaseResponse<String> baseResponse = BaseResponse.<String>builder()
                .data(token)
                .state(StateEnum.SUCCESS.getCode())
                .build();
        return baseResponse;
    }
}
