package com.sac.job.interceptor;

import com.sac.job.configure.redis.RedisUtil;
import com.sac.job.modules.idempotent.annotation.Idempotent;
import com.sac.framework.exception.IdempotentException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

@Component
public class IdempotentInterceptor implements HandlerInterceptor {
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod= (HandlerMethod) handler;
        Method method=handlerMethod.getMethod();
        Idempotent methodAnnotation=method.getAnnotation(Idempotent.class);
        if (methodAnnotation != null){
            // 校验通过放行，校验不通过全局异常捕获后输出返回结果
            //从请求头中获取token
            String token = request.getHeader("Idempotent-Token");
            if (StringUtils.isNotBlank(token)) {
                //如果redis中不包含该token，说明token已经被删除了，抛出请求重复异常
                if (!redisUtil.hasKey(token)) {
                    throw new IdempotentException("请求重复");
                }
                //删除token
                Boolean del = redisUtil.delete(token);
                //如果删除不成功（已经被其他请求删除），抛出请求重复异常
                if (!del) {
                    throw new IdempotentException("请求重复");
                }
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    }
}
