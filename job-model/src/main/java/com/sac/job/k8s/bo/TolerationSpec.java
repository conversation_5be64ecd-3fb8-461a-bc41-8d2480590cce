package com.sac.job.k8s.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 容忍度规格
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TolerationSpec {
    
    /**
     * 键
     */
    private String key;
    
    /**
     * 操作符
     */
    private String operator;
    
    /**
     * 值
     */
    private String value;
    
    /**
     * 效果
     */
    private String effect;
    
    /**
     * 容忍时间（秒）
     */
    private Long tolerationSeconds;
}
