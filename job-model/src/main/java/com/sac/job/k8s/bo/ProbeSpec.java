package com.sac.job.k8s.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 探针规格定义
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProbeSpec {
    
    /**
     * HTTP GET 探针
     */
    private HttpGetProbe httpGet;
    
    /**
     * TCP Socket 探针
     */
    private TcpSocketProbe tcpSocket;
    
    /**
     * 执行命令探针
     */
    private ExecProbe exec;
    
    /**
     * 初始延迟秒数
     */
    private Integer initialDelaySeconds;
    
    /**
     * 超时秒数
     */
    private Integer timeoutSeconds;
    
    /**
     * 检查周期秒数
     */
    private Integer periodSeconds;
    
    /**
     * 成功阈值
     */
    private Integer successThreshold;
    
    /**
     * 失败阈值
     */
    private Integer failureThreshold;
    
    /**
     * HTTP GET 探针
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HttpGetProbe {
        private String path;
        private Integer port;
        private String scheme;
    }
    
    /**
     * TCP Socket 探针
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TcpSocketProbe {
        private Integer port;
    }
    
    /**
     * 执行命令探针
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecProbe {
        private List<String> command;
    }
}
