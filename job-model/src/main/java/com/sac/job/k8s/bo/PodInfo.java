package com.sac.job.k8s.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Pod 基本信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PodInfo {
    
    /**
     * Pod 名称
     */
    private String name;
    
    /**
     * 命名空间
     */
    private String namespace;
    
    /**
     * Pod 状态
     */
    private String phase;
    
    /**
     * Pod IP
     */
    private String podIP;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 创建时间
     */
    private LocalDateTime creationTime;
    
    /**
     * 启动时间
     */
    private LocalDateTime startTime;
    
    /**
     * 标签
     */
    private Map<String, String> labels;
    
    /**
     * 注解
     */
    private Map<String, String> annotations;
    
    /**
     * 容器列表
     */
    private List<ContainerInfo> containers;
    
    /**
     * 重启次数
     */
    private Integer restartCount;
    
    /**
     * 是否就绪
     */
    private Boolean ready;
}
