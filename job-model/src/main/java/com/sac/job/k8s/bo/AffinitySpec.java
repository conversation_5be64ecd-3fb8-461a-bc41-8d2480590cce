package com.sac.job.k8s.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 亲和性规格
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AffinitySpec {
    
    /**
     * 节点亲和性
     */
    private NodeAffinity nodeAffinity;
    
    /**
     * Pod 亲和性
     */
    private PodAffinity podAffinity;
    
    /**
     * Pod 反亲和性
     */
    private PodAntiAffinity podAntiAffinity;
    
    /**
     * 节点亲和性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NodeAffinity {
        private List<NodeSelectorTerm> requiredDuringSchedulingIgnoredDuringExecution;
        private List<PreferredSchedulingTerm> preferredDuringSchedulingIgnoredDuringExecution;
    }
    
    /**
     * Pod 亲和性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PodAffinity {
        private List<PodAffinityTerm> requiredDuringSchedulingIgnoredDuringExecution;
        private List<WeightedPodAffinityTerm> preferredDuringSchedulingIgnoredDuringExecution;
    }
    
    /**
     * Pod 反亲和性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PodAntiAffinity {
        private List<PodAffinityTerm> requiredDuringSchedulingIgnoredDuringExecution;
        private List<WeightedPodAffinityTerm> preferredDuringSchedulingIgnoredDuringExecution;
    }
    
    /**
     * 节点选择器条件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NodeSelectorTerm {
        private Map<String, List<String>> matchExpressions;
        private Map<String, List<String>> matchFields;
    }
    
    /**
     * 首选调度条件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PreferredSchedulingTerm {
        private Integer weight;
        private NodeSelectorTerm preference;
    }
    
    /**
     * Pod 亲和性条件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PodAffinityTerm {
        private Map<String, String> labelSelector;
        private List<String> namespaces;
        private String topologyKey;
    }
    
    /**
     * 加权 Pod 亲和性条件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WeightedPodAffinityTerm {
        private Integer weight;
        private PodAffinityTerm podAffinityTerm;
    }
}
