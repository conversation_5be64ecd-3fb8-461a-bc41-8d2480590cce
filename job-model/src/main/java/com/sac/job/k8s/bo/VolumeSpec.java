package com.sac.job.k8s.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 卷规格定义
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolumeSpec {
    
    /**
     * 卷名称
     */
    private String name;
    
    /**
     * 卷类型
     */
    private String type;
    
    /**
     * 主机路径（hostPath 类型）
     */
    private String hostPath;
    
    /**
     * 配置映射名称（configMap 类型）
     */
    private String configMapName;
    
    /**
     * 密钥名称（secret 类型）
     */
    private String secretName;
    
    /**
     * 持久卷声明名称（persistentVolumeClaim 类型）
     */
    private String pvcName;
    
    /**
     * 空目录（emptyDir 类型）
     */
    private Boolean emptyDir;
}
