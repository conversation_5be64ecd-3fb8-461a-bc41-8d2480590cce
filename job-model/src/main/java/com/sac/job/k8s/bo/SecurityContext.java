package com.sac.job.k8s.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 安全上下文
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecurityContext {
    
    /**
     * 运行用户 ID
     */
    private Long runAsUser;
    
    /**
     * 运行组 ID
     */
    private Long runAsGroup;
    
    /**
     * 是否以非 root 用户运行
     */
    private Boolean runAsNonRoot;
    
    /**
     * 是否只读根文件系统
     */
    private Boolean readOnlyRootFilesystem;
    
    /**
     * 是否允许特权升级
     */
    private Boolean allowPrivilegeEscalation;
    
    /**
     * 是否特权模式
     */
    private Boolean privileged;
}
