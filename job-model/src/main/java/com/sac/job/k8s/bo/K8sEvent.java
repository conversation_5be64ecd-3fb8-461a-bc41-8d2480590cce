package com.sac.job.k8s.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * K8s 事件信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class K8sEvent {
    
    /**
     * 事件名称
     */
    private String name;
    
    /**
     * 命名空间
     */
    private String namespace;
    
    /**
     * 事件类型
     */
    private String type;
    
    /**
     * 事件原因
     */
    private String reason;
    
    /**
     * 事件消息
     */
    private String message;
    
    /**
     * 相关对象类型
     */
    private String involvedObjectKind;
    
    /**
     * 相关对象名称
     */
    private String involvedObjectName;
    
    /**
     * 相关对象命名空间
     */
    private String involvedObjectNamespace;
    
    /**
     * 事件源组件
     */
    private String sourceComponent;
    
    /**
     * 事件源主机
     */
    private String sourceHost;
    
    /**
     * 首次发生时间
     */
    private LocalDateTime firstTimestamp;
    
    /**
     * 最后发生时间
     */
    private LocalDateTime lastTimestamp;
    
    /**
     * 发生次数
     */
    private Integer count;
    
    /**
     * 报告组件
     */
    private String reportingComponent;
    
    /**
     * 报告实例
     */
    private String reportingInstance;
}
