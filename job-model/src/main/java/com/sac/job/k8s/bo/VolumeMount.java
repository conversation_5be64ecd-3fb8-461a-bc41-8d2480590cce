package com.sac.job.k8s.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 卷挂载定义
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolumeMount {
    
    /**
     * 挂载名称
     */
    private String name;
    
    /**
     * 挂载路径
     */
    private String mountPath;
    
    /**
     * 子路径
     */
    private String subPath;
    
    /**
     * 是否只读
     */
    private Boolean readOnly;
}
