package com.sac.job.job.bo;

import com.sac.job.k8s.bo.PodSpec;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Job K8s 配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobK8sConfigBO {

    /**
     * 作业ID
     */
    private String jobId;

    /**
     * 作业名称
     */
    private String jobName;

    /**
     * 命名空间
     */
    private String namespace;

    /**
     * Pod 规格
     */
    private PodSpec podSpec;

    /**
     * 作业类型
     */
    private String jobType;

    /**
     * 并行度
     */
    private Integer parallelism;

    /**
     * 完成数
     */
    private Integer completions;

    /**
     * 超时时间（秒）
     */
    private Long timeoutSeconds;

    /**
     * 重试次数
     */
    private Integer backoffLimit;

    /**
     * 标签
     */
    private Map<String, String> labels;

    /**
     * 注解
     */
    private Map<String, String> annotations;

    /**
     * 是否自动清理
     */
    private Boolean autoCleanup;

    /**
     * 清理延迟时间（秒）
     */
    private Long cleanupDelaySeconds;
}
