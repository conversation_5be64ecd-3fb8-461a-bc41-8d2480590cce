package com.sac.job.fegin.response;

import lombok.*;

/**
 * All rights Reserved, Designed By www.sac.com
 *
 * <AUTHOR>
 * @version V1.0.0
 * @description 响应参数
 * @date 2019/09/03 10:10
 * @copyright 2019 www.sac.com
 * 注意 本内容仅限于国电南自,禁止外泄以及用于其他的商业
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResponseParameterInfo {

    private Integer state;
    private String request;
    private String message;
    private String cause;
    private Object variables;
    private Object data;
}
