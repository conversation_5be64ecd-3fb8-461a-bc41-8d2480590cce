# Kubernetes 集成使用指南

## 概述

本项目集成了 Kubernetes Java Client，提供了完整的 K8s 容器管理功能，包括：

- Pod 生命周期管理
- 容器状态监控
- 资源使用情况查询
- 日志管理
- 事件监控
- 集群资源信息获取

## 配置

### 1. 启用 K8s 功能

在 `application.yml` 中配置：

```yaml
k8s:
  enabled: true  # 启用 K8s 功能
```

### 2. 连接配置

#### 集群内运行（推荐生产环境）

```yaml
k8s:
  config:
    in-cluster: true  # 使用 ServiceAccount
```

#### 集群外运行（开发环境）

```yaml
k8s:
  config:
    in-cluster: false
    kubeconfig: ~/.kube/config  # kubeconfig 文件路径
```

### 3. 完整配置示例

```yaml
k8s:
  enabled: true
  config:
    in-cluster: false
    kubeconfig: ~/.kube/config
    namespace: default
    connection-timeout: 30000
    read-timeout: 60000
  monitoring:
    check-interval: 30
    resource-check-interval: 60
    resource-monitoring-enabled: true
  job:
    default-timeout: 3600
    default-backoff-limit: 3
    auto-cleanup: true
    cleanup-delay: 300
  resources:
    cpu-threshold: 80
    memory-threshold: 80
    default-cpu-request: 100m
    default-memory-request: 128Mi
    default-cpu-limit: 500m
    default-memory-limit: 512Mi
```

## API 使用

### 1. 集群资源信息

```bash
GET /api/k8s/cluster/resources
```

返回集群的 CPU、内存、存储等资源使用情况。

### 2. 节点管理

```bash
# 获取所有节点
GET /api/k8s/nodes
```

### 3. Pod 管理

```bash
# 获取所有 Pod
GET /api/k8s/pods

# 获取指定命名空间的 Pod
GET /api/k8s/pods?namespace=default

# 获取 Pod 详细信息
GET /api/k8s/pods/{namespace}/{podName}

# 创建 Pod
POST /api/k8s/pods
Content-Type: application/json

{
  "name": "test-pod",
  "namespace": "default",
  "labels": {
    "app": "test-app"
  },
  "containers": [
    {
      "name": "test-container",
      "image": "nginx:latest",
      "resources": {
        "cpuRequest": 100,
        "memoryRequest": 134217728,
        "cpuLimit": 200,
        "memoryLimit": 268435456
      }
    }
  ]
}

# 删除 Pod
DELETE /api/k8s/pods/{namespace}/{podName}
```

### 4. 日志管理

```bash
# 获取 Pod 日志
GET /api/k8s/pods/{namespace}/{podName}/logs

# 获取指定容器日志
GET /api/k8s/pods/{namespace}/{podName}/logs?containerName=test-container

# 获取最后 100 行日志
GET /api/k8s/pods/{namespace}/{podName}/logs?tailLines=100
```

### 5. 事件监控

```bash
# 获取 Pod 事件
GET /api/k8s/pods/{namespace}/{podName}/events
```

### 6. 扩缩容

```bash
# 扩缩容 Deployment
PUT /api/k8s/deployments/{namespace}/{deploymentName}/scale?replicas=3
```

### 7. 健康检查

```bash
# 检查 K8s 连接状态
GET /api/k8s/health
```

## 编程接口

### 1. 注入 K8sService

```java
@Autowired
private K8sService k8sService;
```

### 2. 检查资源

```java
// 检查集群资源是否充足
boolean hasEnoughResources = !k8sService.notEnoughResources();

// 获取集群资源详情
ClusterResourceInfo clusterInfo = k8sService.getClusterResources();
```

### 3. Pod 管理

```java
// 创建 Pod
PodSpec podSpec = PodSpec.builder()
    .name("my-pod")
    .namespace("default")
    .containers(Arrays.asList(containerSpec))
    .build();
String podName = k8sService.createPod(podSpec, "default");

// 获取 Pod 信息
PodInfo podInfo = k8sService.getPodInfo(podName, "default");

// 删除 Pod
boolean deleted = k8sService.deletePod(podName, "default");
```

### 4. 监控 Pod 状态

```java
// 同步监听
k8sService.watchPodStatus("default", podInfo -> {
    System.out.println("Pod 状态变化: " + podInfo.getName() + " - " + podInfo.getPhase());
});

// 异步监听
CompletableFuture<Void> future = k8sService.watchPodStatusAsync("default", podInfo -> {
    // 处理状态变化
});
```

### 5. 运行作业

```java
// 构建作业配置
JobK8sConfigBO jobConfig = JobK8sConfigBO.builder()
    .jobId("job-001")
    .jobName("test-job")
    .namespace("default")
    .podSpec(podSpec)
    .autoCleanup(true)
    .cleanupDelaySeconds(300L)
    .build();

// 运行作业
k8sService.run(jobConfig);
```

## 权限配置

### ServiceAccount 权限

如果在集群内运行，需要为 ServiceAccount 配置适当的权限：

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: job-manager
rules:
- apiGroups: [""]
  resources: ["pods", "nodes", "events"]
  verbs: ["get", "list", "watch", "create", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "patch"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "create", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: job-manager-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: job-manager
subjects:
- kind: ServiceAccount
  name: default
  namespace: default
```

## 故障排除

### 1. 连接问题

- 检查 kubeconfig 文件路径是否正确
- 确认 K8s 集群可访问
- 验证网络连接

### 2. 权限问题

- 检查 ServiceAccount 权限
- 确认 RBAC 配置正确

### 3. 资源问题

- 检查集群资源是否充足
- 确认节点状态正常

### 4. 日志查看

```bash
# 查看应用日志
kubectl logs -f deployment/job-provider

# 查看 Pod 事件
kubectl describe pod <pod-name>
```

## 注意事项

1. **生产环境建议使用 ServiceAccount 方式连接**
2. **合理设置资源请求和限制**
3. **启用自动清理避免资源泄露**
4. **监控集群资源使用情况**
5. **定期检查 Pod 状态和事件**
