package com.sac.framework.exception;

/**
 * @author: JSQ
 * @date: 23/3/2020 上午10:16
 * @description: 参数异常，检查异常
 * @copyright: 2020 南京华盾电力信息安全测评有限公司 All rights reserved.
 */
public class ArgumentException extends Exception {

    public ArgumentException(String message){
        super(message);
    }

    public ArgumentException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public ArgumentException(Throwable throwable) {
        super(throwable);
    }

}
