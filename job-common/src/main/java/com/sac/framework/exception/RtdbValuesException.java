package com.sac.framework.exception;

import java.util.List;

/**
 * @ClassName: RtdbValuesException
 * @author: JSQ
 * @date: 4/28/2019 11:33 AM
 * @Description: 实时库查询值类异常，非检查异常
 * @Copyright: 2019 南京华盾电力信息安全测评有限公司 All rights reserved.
 */
public class RtdbValuesException extends RuntimeException {

    private List<String> invalidPoints;

    public RtdbValuesException(){
        super();
    }

    public RtdbValuesException(String message){
        super(message);
    }

    public RtdbValuesException(String message, List<String> invalidPoints){
        super(message);
        this.invalidPoints = invalidPoints;
    }
}
