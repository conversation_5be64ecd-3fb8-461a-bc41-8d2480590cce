package com.sac.framework.yaml;

import org.springframework.context.annotation.PropertySource;
import org.springframework.core.annotation.AliasFor;
import org.springframework.core.io.support.PropertySourceFactory;

import java.lang.annotation.*;

/**
 * @author: JSQ
 * @date: 5/7/2019 下午4:01
 * @description: TODO
 * @copyright: 2019 南京华盾电力信息安全测评有限公司 All rights reserved.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@PropertySource(value = "")
public @interface YamlSource {

    @AliasFor(annotation = PropertySource.class, attribute = "name")
    String name() default "";

    @AliasFor(annotation = PropertySource.class, attribute = "value")
    String[] value();

    @AliasFor(annotation = PropertySource.class, attribute = "ignoreResourceNotFound")
    boolean ignoreResourceNotFound() default false;

    @AliasFor(annotation = PropertySource.class, attribute = "encoding")
    String encoding() default "";

    @AliasFor(annotation = PropertySource.class, attribute = "factory")
    Class<? extends PropertySourceFactory> factory() default PropertySourceFactory.class;
}