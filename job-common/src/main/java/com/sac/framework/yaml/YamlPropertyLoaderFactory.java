package com.sac.framework.yaml;

import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.support.DefaultPropertySourceFactory;
import org.springframework.core.io.support.EncodedResource;

import java.io.IOException;
import java.util.List;

/**
 * @author: JSQ
 * @date: 5/7/2019 下午4:01
 * @description: TODO
 * @copyright: 2019 南京华盾电力信息安全测评有限公司 All rights reserved.
 */
public class YamlPropertyLoaderFactory extends DefaultPropertySourceFactory {


    @Override
    public PropertySource<?> createPropertySource(String name, EncodedResource resource) throws IOException {
        if (null == resource){
            super.createPropertySource(name, resource);
        }
        List<PropertySource<?>> propertySources = new YamlPropertySourceLoader().load(resource.getResource().getFilename(), resource.getResource());
        if (null != propertySources && !propertySources.isEmpty()){
            return propertySources.get(0);
        }
        //
        return null;
    }
}