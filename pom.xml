<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sac</groupId>
    <artifactId>job</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>job</name>

    <properties>
        <!-- 脚手架版本 -->
        <revision>1.1.6.0</revision>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring.boot.version>2.3.12.RELEASE</spring.boot.version>
        <spring-context.version>5.2.15.RELEASE</spring-context.version>
        <openfeign.version>2.2.9.RELEASE</openfeign.version>
        <slf4j.version>1.7.9</slf4j.version>
        <logback.version>1.2.3</logback.version>
        <mysql.version>8.0.16</mysql.version>
        <mssql-jdbc.version>6.4.0.jre8</mssql-jdbc.version>
        <oracle-version>********.0-atlassian-hosted</oracle-version>
        <postgresql.version>42.3.6</postgresql.version>
        <druid.version>1.2.4</druid.version>
        <!--        <easypoi.version>3.1.0</easypoi.version>-->
        <commons.lang3.version>3.9</commons.lang3.version>
        <validator.version>6.0.16.Final</validator.version>
        <jackson.version>2.11.4</jackson.version>
        <swagger.version>2.9.2</swagger.version>
        <lombok.version>1.16.14</lombok.version>
        <joda-time-version>2.10.1</joda-time-version>
        <ibps.cloud.version>3.5.0-LC.RELEASE</ibps.cloud.version>
        <mybatisplus-version>3.4.2</mybatisplus-version>
        <commons.beanutils.version>1.9.3</commons.beanutils.version>
        <nacos-config.version>2.2.7.RELEASE</nacos-config.version>
        <commons-pool2.version>2.11.1</commons-pool2.version>
        <job.version>1.0.0</job.version>
        <log4j2.version>2.6.6</log4j2.version>
        <apollo.version>1.5.1</apollo.version>
        <xxl-job.version>2.3.1-SNAPSHOT</xxl-job.version>
        <license-check.version>1.0.0</license-check.version>
        <spring.cloud.version>2.2.9.RELEASE</spring.cloud.version>
        <jakarta.version>2.0.2</jakarta.version>
        <consul.discovery.version>2.2.8.RELEASE</consul.discovery.version>
        <nacos.discovery.version>2.2.9.RELEASE</nacos.discovery.version>
        <nacos.client.version>1.4.2</nacos.client.version>
        <authorization.version>1.0.0</authorization.version>
        <ribbon.version>2.3.0</ribbon.version>
        <kingbase.version>8.6.0</kingbase.version>
        <dm.version>1.8</dm.version>
        <io-seata.version>2.0.0</io-seata.version>
        <alibaba-seata.version>2.2.7.RELEASE</alibaba-seata.version>
        <!--<redis.version>3.0.504</redis.version>-->
    </properties>

    <!--项目子模块-->
    <modules>
        <module>job-http</module>
        <module>job-core</module>
        <module>job-common</module>
        <module>job-model</module>
        <module>job-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <!--modules-->
            <dependency>
                <groupId>com.sac</groupId>
                <artifactId>job-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sac</groupId>
                <artifactId>job-model</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sac</groupId>
                <artifactId>job-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sac</groupId>
                <artifactId>job-client</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Hoxton.SR12</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--aop-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <!-- 去掉springboot默认配置-->
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring-context.version}</version>
            </dependency>

            <!--Redis-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!--datasource -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!--SqlServer-->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql-jdbc.version}</version>
            </dependency>

            <!--Oracle-->
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>${oracle-version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!--postgresql-->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!-- kingbase8 -->
            <dependency>
                <groupId>cn.com.kingbase</groupId>
                <artifactId>kingbase8</artifactId>
                <version>${kingbase.version}</version>
            </dependency>

            <!-- dm -->
            <dependency>
                <groupId>dm.jdbc</groupId>
                <artifactId>DmJdbcDriver</artifactId>
                <version>${dm.version}</version>
            </dependency>

            <!--mybatisplus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus-version}</version>
            </dependency>

            <!--log-->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!--log4j2-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j2</artifactId>
                <version>${log4j2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- swagger -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!--工具-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons.beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${validator.version}</version>
            </dependency>

            <!-- json -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!--lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!--joda-->
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time-version}</version>
            </dependency>

            <!--xxl-job-->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!--license check-->
            <dependency>
                <groupId>com.sac</groupId>
                <artifactId>license-spring-boot-starter</artifactId>
                <version>${license-check.version}</version>
            </dependency>

            <!-- config -->
            <!-- apollo client -->
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>

            <!-- nacos client -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>

            <!-- discovery -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${nacos.discovery.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-consul-discovery</artifactId>
                <version>${consul.discovery.version}</version>
            </dependency>

            <!-- feign -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-openfeign-core</artifactId>
                <version>${openfeign.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${openfeign.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-loadbalancer</artifactId>
                <version>${spring.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>com.netflix.ribbon</groupId>
                <artifactId>ribbon-core</artifactId>
                <version>${ribbon.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-commons</artifactId>
                <version>${spring.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sac.auth</groupId>
                <artifactId>authorization-springboot-starter</artifactId>
                <version>${authorization.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!-- ibps -->
            <dependency>
                <groupId>com.lc.ibps.persist</groupId>
                <artifactId>ibps-persist-common</artifactId>
                <version>${ibps.cloud.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lc.ibps.api</groupId>
                <artifactId>ibps-api-base</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.lc.ibps.base</groupId>
                <artifactId>ibps-base-cloud</artifactId>
                <version>${revision}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.lc.ibps.base</groupId>
                        <artifactId>ibps-base-db</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.lc.ibps.persist</groupId>
                        <artifactId>ibps-persist-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- seata -->
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${io-seata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
                <version>${alibaba-seata.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!--多环境配置-->
    <profiles>
        <!--本地-->
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--测试-->
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
        </profile>
        <!--预发-->
        <profile>
            <id>pre</id>
            <properties>
                <env>pre</env>
            </properties>
        </profile>
        <!--生产-->
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>job</finalName>
        <plugins>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <skip>true</skip>
                    <compilerId>javac</compilerId>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <compilerVersion>1.8</compilerVersion>
                    <verbose>true</verbose>
                    <optimize>true</optimize>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.plexus</groupId>
                        <artifactId>plexus-compiler-eclipse</artifactId>
                        <version>2.2</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <!--<failOnMissingWebXml>false</failOnMissingWebXml>-->
                    <includeEmptyDirs>true</includeEmptyDirs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warSourceExcludes>upload/**</warSourceExcludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.0.1</version>
            </plugin>

        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <!--                    <include>**/*.yml</include>-->
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project>