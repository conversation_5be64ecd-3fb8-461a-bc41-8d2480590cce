<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>job</artifactId>
        <groupId>com.sac</groupId>
        <version>${revision}</version>
    </parent>

    <groupId>com.sac</groupId>
    <artifactId>job-client</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lc.ibps.base</groupId>
            <artifactId>ibps-base-cloud</artifactId>
        </dependency>

        <dependency>
            <groupId>com.netflix.ribbon</groupId>
            <artifactId>ribbon-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sac</groupId>
            <artifactId>job-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sac</groupId>
            <artifactId>job-model</artifactId>
        </dependency>

    </dependencies>


</project>