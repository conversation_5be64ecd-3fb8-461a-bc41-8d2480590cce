package com.sac.job.client;

import com.sac.framework.response.BaseResponse;
import com.sac.job.client.pojo.ImageVO;
import com.sac.job.client.pojo.JobParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "sac-job-provider")
public interface K8sClient {

    @GetMapping("/api/jobs/k8s/images")
    BaseResponse<List<ImageVO>> listImages();

}
