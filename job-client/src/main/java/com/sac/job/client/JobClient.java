package com.sac.job.client;

import com.sac.framework.response.BaseResponse;
import com.sac.framework.response.PageList;
import com.sac.job.client.pojo.JobParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "sac-job-provider")
public interface JobClient {

    @PostMapping("/api/jobs/exec")
    BaseResponse exec(@RequestBody JobParams jobParams);

    @PostMapping("/api/jobs/debug")
    BaseResponse debug(@RequestBody JobParams jobParams);
}
